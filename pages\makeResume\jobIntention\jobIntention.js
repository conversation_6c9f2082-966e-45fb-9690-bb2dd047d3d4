const app = getApp();
const ResumeFormHelper = require('../../../utils/resume/ResumeFormHelper.js');

Page({
  data: {
    jobIntentionFormData: null, // 将在 onLoad 中初始化为 JobIntention 实例
    salaryRange: [
      '3k以下', '3-5k', '5-7k', '7-10k',
      '10-15k', '15-20k', '20-30k', '30-50k',
      '50k以上', '面议'
    ]
  },

  onLoad() {

    this.loadJobIntentionData();
  },

  /**
   * 从全局管理器加载求职意向数据
   */
  loadJobIntentionData() {
    try {
      const jobIntentionData = ResumeFormHelper.loadFieldData('jobIntention', app);

      this.setData({
        jobIntentionFormData: jobIntentionData
      });


    } catch (error) {
      console.error('❌ 加载求职意向数据失败:', error);
      // 出错时使用空数据
      const emptyData = ResumeFormHelper.getEmptyFieldData('jobIntention');
      this.setData({
        jobIntentionFormData: emptyData
      });
    }
  },

  // 输入框内容变化时触发
  handleInput(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;

    this.setData({
      [`jobIntentionFormData.${field}`]: value
    });
  },

  // 薪资选择器变化时触发
  handleSalaryChange(e) {
    const value = this.data.salaryRange[e.detail.value];
    this.setData({
      'jobIntentionFormData.salary': value
    });
  },

  // 清除字段内容
  clearField(e) {
    const { field } = e.currentTarget.dataset;
    this.setData({
      [`jobIntentionFormData.${field}`]: ''
    });
  },

  /**
   * 保存求职意向信息
   */
  saveInfo() {
    try {
      console.log('=== 保存求职意向信息 ===');
      console.log('保存的数据:', this.data.jobIntentionFormData);

      const success = ResumeFormHelper.saveFieldData('jobIntention', this.data.jobIntentionFormData, app);

      if (success) {
        wx.showToast({
          title: '保存成功',
          icon: 'success',
          duration: 1500
        });



        // 延迟返回，让用户看到保存成功的提示
        setTimeout(() => {
          wx.navigateBack();
        }, 500);
      } else {
        wx.showToast({
          title: '保存失败',
          icon: 'none'
        });
      }

    } catch (error) {
      console.error('❌ 保存求职意向失败:', error);
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    }
  },

  /**
   * 删除求职意向信息
   */
  deleteInfo() {
    wx.showModal({
      title: '提示',
      content: '确定要删除求职意向信息吗？',
      success: (res) => {
        if (res.confirm) {
          try {
            console.log('=== 删除求职意向信息 ===');

            const success = ResumeFormHelper.clearFieldData('jobIntention', app);

            if (success) {
              // 重置表单数据
              const emptyData = ResumeFormHelper.getEmptyFieldData('jobIntention');
              this.setData({
                jobIntentionFormData: emptyData
              });

              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });



              setTimeout(() => {
                wx.navigateBack();
              }, 500);
            } else {
              wx.showToast({
                title: '删除失败',
                icon: 'none'
              });
            }

          } catch (error) {
            console.error('❌ 删除求职意向失败:', error);
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            });
          }
        }
      }
    });
  }
});