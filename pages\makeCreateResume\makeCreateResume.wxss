/* pages/makeCreateResume/makeCreateResume.wxss */
page {
  background-color: #f5f5f5;
  height: 100%;
}

.resume-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 固定的预览容器 - 占据主要空间，为底部组件留出空间 */
.fixed-preview-container {
  flex: 1;
  overflow: hidden;
  position: relative;
  /* 为底部模板选择器(200rpx)和工具栏(110rpx)留出空间 */
  padding-bottom: 330rpx;
}

.preview-area {
  width: 100%;
  height: 100%;
}

/* 隐藏HTML生成器 */
.hidden-generator {
  position: absolute;
  left: -9999px;
  visibility: hidden;
  pointer-events: none;
}

/* 分享弹窗样式 */
.share-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.share-modal.show {
  opacity: 1;
  visibility: visible;
}

.share-modal-content {
  background: #fff;
  border-radius: 20rpx;
  width: 600rpx;
  max-width: 90%;
  transform: scale(0.8);
  transition: transform 0.3s ease;
}

.share-modal.show .share-modal-content {
  transform: scale(1);
}

.share-modal-header {
  padding: 40rpx 40rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  text-align: center;
  position: relative;
}

.share-modal-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
}

.close-btn {
  position: absolute;
  right: 30rpx;
  top: 30rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f5f5f5;
}

.close-icon {
  font-size: 36rpx;
  color: #666;
}

.share-modal-body {
  padding: 40rpx;
}

.share-tip {
  text-align: center;
  margin-bottom: 40rpx;
}

.share-tip text {
  font-size: 28rpx;
  color: #666;
}

.share-actions {
  display: flex;
  gap: 20rpx;
}

.share-btn {
  flex: 1;
  height: 100rpx;
  border-radius: 16rpx;
  border: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  font-size: 24rpx;
}

.share-btn.primary {
  background: #1aad19;
  color: #fff;
}

.share-btn.secondary {
  background: #f5f5f5;
  color: #333;
}

.btn-icon {
  font-size: 32rpx;
}

.btn-text {
  font-size: 24rpx;
}
