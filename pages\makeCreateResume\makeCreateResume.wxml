<view class="resume-page">

  <!-- 固定的简历预览区域 -->
  <view class="fixed-preview-container">
    <resume-preview
      id="resume-preview"
      class="preview-area"
      resumeData="{{resumeData}}"
      config="{{config}}"
      template="{{template}}"
    />
  </view>

  <!-- 模板选择器 - 使用fixed定位 -->
  <template-selector
    id="template-selector"
    selected="{{template.id}}"
    bind:select="handleTemplateSelect"
  />

  <!-- 工具栏 - 使用fixed定位 -->
  <tool-bar
    config="{{config}}"
    bind:configChange="handleConfigChange"
    bind:download="handleGeneratePDF"
    bind:rename="handleRename"
  />

  <!-- 分享弹窗 -->
  <view class="share-modal {{showShareModal ? 'show' : ''}}" bindtap="closeShareModal">
    <view class="share-modal-content" catchtap="">
      <view class="share-modal-header">
        <text class="share-modal-title">PDF生成成功</text>
        <view class="close-btn" bindtap="closeShareModal">
          <text class="close-icon">×</text>
        </view>
      </view>
      <view class="share-modal-body">
        <view class="share-tip">
          <text>选择您要进行的操作：</text>
        </view>
        <view class="share-actions">
          <button class="share-btn primary" bindtap="shareToWechat" open-type="">
            <text class="btn-icon">📤</text>
            <text class="btn-text">分享给好友</text>
          </button>
          <button class="share-btn secondary" bindtap="saveToAlbum">
            <text class="btn-icon">💾</text>
            <text class="btn-text">保存到本地</text>
          </button>
        </view>
      </view>
    </view>
  </view>

</view>